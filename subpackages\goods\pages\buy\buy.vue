<template>
  <view class="buy-container">
    <!-- 顶部标题区域 -->
    <view class="header-section">
      <view class="header-content">
        <view class="header-title">ETC特权办理</view>
        <view class="header-subtitle">享受便捷出行服务</view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-wrapper">
      <!-- 价格卡片 -->
      <view class="price-card">
        <view class="price-section">
          <view class="price-label">会员价格</view>
          <view class="price-amount">
            <text class="currency">¥</text>
            <text class="amount">{{ goods.money }}</text>
          </view>
          <view class="order-status">
            {{ orderno === "" ? "请阅读特权说明" : "订单号: " + orderno }}
          </view>
        </view>
      </view>

      <!-- 产品信息卡片 -->
      <view class="product-card">
        <view class="product-header">
          <view class="product-name">{{ goods.name }}</view>
        </view>
        <view class="product-divider"></view>
        <view class="product-description">
          <view class="desc-label">特权说明:</view>
          <view class="desc-content">{{ goods.msg }}</view>
        </view>
      </view>

      <!-- 权益展示卡片 -->
      <view class="benefits-card">
        <view class="benefits-header">
          <view class="benefits-title">激活后解锁以下权益</view>
        </view>

        <view class="benefits-grid">
          <view
            v-for="(data, index) in goodsIcon"
            :key="index"
            class="benefit-item"
            :class="{ 'benefit-active': index === clickEquity }"
            @click="selectBenefit(data, index)"
          >
            <view class="benefit-icon">
              <image :src="data.icon" class="icon-image"></image>
            </view>
            <view class="benefit-title">{{ data.title }}</view>
          </view>
        </view>

        <view class="benefit-explain">
          <view class="explain-header">
            <uni-icons type="info" size="16" color="#00d4aa"></uni-icons>
            <text class="explain-title">权益说明</text>
          </view>
          <view class="explain-content">{{ explain }}</view>
        </view>
      </view>
    </view>

    <!-- 底部支付栏 -->
    <view class="payment-footer">
      <!-- 用户协议区域 -->
      <view class="agreement-section">
        <view class="agreement-content">
          <view
            @click="changeAgree"
            class="agreement-checkbox"
            :class="{ 'agreement-checked': isAgree }"
          >
            <uni-icons
              v-if="isAgree"
              type="checkmarkempty"
              size="14"
              color="#fff"
            ></uni-icons>
          </view>
          <text class="agreement-text">我已阅读并同意</text>
          <text class="agreement-link" @click="showAgreement"
            >《用户服务协议》</text
          >
        </view>
      </view>

      <!-- 支付操作区域 -->
      <view class="payment-content">
        <view class="payment-method">
          <view class="payment-icon">
            <image
              :src="`${config.BASE_URL}/static/wx.png`"
              class="wx-icon"
            ></image>
          </view>
          <text class="payment-text">微信支付</text>
        </view>
        <view class="payment-action">
          <view class="submit-btn" @click="toLookCar">
            <uni-icons type="wallet" size="16" color="#fff"></uni-icons>
            <text>下一步</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { saveOrder, AppPayForPrepayid, ifOrderIsComplete } from "@/api/shop.js";
import { fuioupay } from "@/api/fuiou.js";
import { mapGetters, mapActions } from "vuex";
import { isLogin, getUserId } from "@/utils/auth.js";
import config from "@/utils/config.js";

const goodsIcon = [
  {
    icon: `${config.BASE_URL}/static/ETC2.png`,
    title: "赠送ETC",
    explain: "赠送ETC",
  },
  {
    icon: `${config.BASE_URL}/static/ETC2.png`,
    title: "加油打折",
    explain: "使用app内的加油链接加油享受打折服务",
  },
  {
    icon: `${config.BASE_URL}/static/ETC2.png`,
    title: "极速快递",
    explain: "单单享受优惠寄收快递",
  },
  {
    icon: `${config.BASE_URL}/static/ETC2.png`,
    title: "ETC商城",
    explain: "ETC商城开通各大平台会员尽享打折优惠",
  },
];

export default {
  data() {
    return {
      goods: {
        id: 0,
        name: "",
        msg: "",
      },
      orderno: "",
      goodsIcon,
      clickEquity: 0,
      explain: "赠送ETC",
      carId: "",
      isAgree: false, // 是否同意用户协议
    };
  },
  methods: {
    // 改变协议勾选状态
    changeAgree() {
      this.isAgree = !this.isAgree;
    },
    // 显示协议页面
    showAgreement() {
      uni.navigateTo({
        url: "/subpackages/common/pages/userProtocol/userProtocol",
      });
    },
    // 选择权益
    selectBenefit(data, index) {
      this.explain = data.explain;
      this.clickEquity = index;
    },
    showCar() {
      // this.$refs.popup.open()
      // 跳转绑定车牌信息界面
      uni.navigateTo({
        url: "/pages/bindingCarId/bindingCarId",
      });
    },
    toLookCar() {
      // 检查用户是否勾选协议
      if (!this.isAgree) {
        uni.showToast({
          duration: 1000,
          title: "请阅读并同意用户服务协议",
          icon: "none",
        });
        return;
      } // uni.showLoading({
      // 	title: "加载中"
      // })
      if (!isLogin()) {
        uni.showToast({
          duration: 1000,
          title: "请先登录",
          icon: "error",
        });
        return;
      }

      // 获取用户ID
      const userId = getUserId();
      if (!userId) {
        uni.showToast({
          title: "请先登录",
          icon: "error",
        });
        return;
      }

      let data = {
        userId: userId,
        name: this.goods.name,
        type: "0",
        // 待添加金额字段
        amt: this.goods.money,
      };
      console.log(data);
      // 调起富友支付
      console.log("发起支付");
      fuioupay(data).then((res) => {
        // uni.hideLoading()
        // 如果返回code == 200
        if (res.data.code == "200") {
          console.log("请求成功");
          let redata = res.data;

          console.log("开始调起微信支付");

          // 如果已经在微信环境中，直接调用
          console.log(redata);
          uni.requestPayment({
            appId: res.data.data.appid, // 公众号ID，由商户传入
            timeStamp: res.data.data.timestamp, // 时间戳，自1970年以来的秒数
            nonceStr: res.data.data.noncestr, // 随机串
            package: res.data.data.package,
            signType: res.data.data.signtype, // 微信签名方式
            paySign: res.data.data.paysign, // 微信签名
            success: function (res) {
              console.log(res); //res.errMsg == "requestPayment:ok"
              console.log("success:" + JSON.stringify(res));

              // // 跳转其他平台
              // uni.navigateToMiniProgram({
              // 	// appid  写你要跳转的小程序的 appid
              // 	appId: 'wxb6bf2fbaa8ebca63',
              // 	// 路径写  src下的路径,假如你跳转的是pages下的页面,就可以写pages/index
              // 	path: '/pages/index/memberShip',
              // 	extraData: {
              // 		'type': 'out',
              // 		'inviterId': '106505',
              // 		'wechatType': '2',
              // 		'pakageType': '4'
              // 	},
              // 	// 这个不写的话会显示开发环境,不能正常跳转,写上就能正常跳转了
              // 	envVersion: 'release',
              // 	success(res) {
              // 		// 打开成功
              // 		uni.showToast({
              // 			title: '跳转成功'
              // 		})
              // 	},
              // 	fail(err) {
              // 		uni.switchTab({
              // 			url: '../my/my'
              // 		})
              // 	}
              // })
              let _this = this;
              const expressConfig = config.MINI_PROGRAM_JUMP_CONFIG.EXPRESS_MINI_PROGRAM;
              let toPath =
                `/pages/empty_hair/new_module/select_etc_handle_type/select_etc_handle_type?isNewTrucks=0&shopId=${expressConfig.SHOP_ID}&vehPlates=${_this.carId}&extend={"merchantCode":"${expressConfig.MERCHANT_CODE}"}`;
              uni.navigateToMiniProgram({
                // appid  写你要跳转的小程序的 appid
                appId: expressConfig.APP_ID,
                // 路径写  src下的路径,假如你跳转的是pages下的页面,就可以写pages/index
                path: toPath,
                extraData: {
                  type: "out",
                },
                // 这个不写的话会显示开发环境,不能正常跳转,写上就能正常跳转了
                envVersion: expressConfig.ENV_VERSION,
                success(res) {
                  // 打开成功
                  uni.showToast({
                    title: "跳转成功",
                  });
                },
                fail(err) {
                  uni.switchTab({
                    url: "../my/my",
                  });
                },
              });
            },
            fail: function (err) {
              console.log("fail:" + JSON.stringify(err));
            },
            complete(err) {
              console.log("complete:" + JSON.stringify(err));
            },
          });
        } else {
          uni.showToast({
            title: "请求失败",
            icon: "error",
          });
        }
      });
    },

    payGoods() {
      // 生成过订单
      if (this.orderno !== "") {
        this.wxPay(this.orderno);
        return;
      }
      // 没有生成过订单
      const data = {
        phone: this.userInfo.phone,
        equity_type: this.goods.id,
      };
      uni.showLoading({
        title: "生成订单中",
      });
      saveOrder(data)
        .then((res) => {
          uni.hideLoading();
          console.log(res);
          res = res.data;
          if (res.code !== 200) {
            return;
          }
          this.orderno = res.data.orderno;
          this.wxPay(this.orderno);
        })
        .catch(() => {
          uni.hideLoading();
        });
    },
    wxPay(orderno) {
      console.log(orderno);
      AppPayForPrepayid({
        orderno,
      })
        .then((res) => {
          console.log(res);
          res = res.data;
          if (res.code !== 200) {
            return Promise.reject({
              msg: "支付失败",
            });
          }
          return Promise.resolve(res.data);
        })
        .then((data) => {
          console.log(data);
          const orderInfo = {
            appid: data.appid,
            noncestr: data.nonceStr,
            package: "Sign=WXPay",
            partnerid: data.partnerid,
            prepayid: data.prepayid,
            timestamp: data.timestamp,
            sign: data.paySign,
          };
          uni.requestPayment({
            provider: "wxpay",
            orderInfo,
            success: (res) => {
              console.log(res);
              this.checkOrderStatus();
            },
            fail: (e) => {
              if (e.code === -8) {
                uni.showToast({
                  title: "未安装微信",
                  icon: "error",
                  duration: 1500,
                });
                return;
              }
            },
          });
        });
    },
    checkOrderStatus() {
      const data = {
        orderno: this.orderno,
      };
      uni.showLoading({
        title: "加载中",
      });
      let it = null;
      it = setInterval(() => {
        ifOrderIsComplete(data).then((res) => {
          console.log(res);
          res = res.data;
          if (res.data === "complete") {
            uni.hideLoading();
            clearInterval(it);
            this["user/getUserInfo"]();
            uni.showToast({
              title: "充值成功",
              icon: "success",
              duration: 1500,
              complete: () => {
                uni.reLaunch({
                  url: "/pages/my/my",
                });
              },
            });
          }
        });
      }, 2000);
    },
    ...mapActions(["user/getUserInfo"]),
  },
  onLoad(query) {
    this.goods = JSON.parse(query.goods);
    console.log(this.goods);
    this.carId = JSON.parse(query.carId);
    console.log(this.carId);
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  onLaunch: function () {
    // 检查是否是在微信环境中
    if (window.WeixinJSBridge) {
      // 已经在微信环境中，直接调用支付准备函数
      onBridgeReady();
    } else {
      // 监听微信JS接口安全域名加载完成
      if (document.addEventListener) {
        document.addEventListener("WeixinJSBridgeReady", onBridgeReady, false);
      } else if (document.attachEvent) {
        document.attachEvent("WeixinJSBridgeReady", onBridgeReady);
        document.attachEvent("onWeixinJSBridgeReady", onBridgeReady);
      }
    }
  },
};
</script>

<style lang="scss" scoped>
/* 页面容器 */
.buy-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
  padding-bottom: 160px; /* 增加底部padding以适应新的底部栏高度 */
}

/* 顶部标题区域 */
.header-section {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 40px 20px 30px;
  color: white;

  .header-content {
    .header-title {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
      letter-spacing: 0.5px;
    }

    .header-subtitle {
      font-size: 14px;
      opacity: 0.9;
      font-weight: 400;
    }
  }
}

/* 主要内容区域 */
.content-wrapper {
  padding: 20px;
  padding-bottom: 40px; /* 为底部固定栏留出更多空间 */
}

/* 价格卡片 */
.price-card {
  background: white;
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 212, 170, 0.15);
  text-align: center;

  .price-section {
    .price-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 10px;
    }

    .price-amount {
      display: flex;
      align-items: baseline;
      justify-content: center;
      margin-bottom: 15px;

      .currency {
        font-size: 24px;
        color: #00d4aa;
        font-weight: 500;
      }

      .amount {
        font-size: 48px;
        font-weight: 700;
        color: #00d4aa;
        margin-left: 5px;
      }
    }

    .order-status {
      font-size: 14px;
      color: #999;
      padding: 8px 16px;
      background: rgba(0, 212, 170, 0.1);
      border-radius: 20px;
      display: inline-block;
    }
  }
}

/* 产品信息卡片 */
.product-card {
  background: white;
  border-radius: 16px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);

  .product-header {
    .product-name {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .product-divider {
    height: 1px;
    background: #f0f0f0;
    margin: 20px 0;
  }

  .product-description {
    .desc-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .desc-content {
      font-size: 15px;
      color: #333;
      line-height: 1.6;
    }
  }
}

/* 权益展示卡片 */
.benefits-card {
  background: white;
  border-radius: 16px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);

  .benefits-header {
    .benefits-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
    }
  }

  .benefits-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 25px;

    .benefit-item {
      flex: 1;
      min-width: 80px;
      text-align: center;
      padding: 15px 10px;
      border-radius: 12px;
      border: 2px solid transparent;
      transition: all 0.3s ease;
      cursor: pointer;

      &.benefit-active {
        border-color: #00d4aa;
        background: rgba(0, 212, 170, 0.05);
        transform: translateY(-2px);
      }

      .benefit-icon {
        margin-bottom: 8px;

        .icon-image {
          width: 40px;
          height: 40px;
          border-radius: 8px;
        }
      }

      .benefit-title {
        font-size: 12px;
        color: #666;
        font-weight: 500;
      }
    }
  }

  .benefit-explain {
    background: rgba(0, 212, 170, 0.05);
    border-radius: 12px;
    padding: 20px;
    border-left: 4px solid #00d4aa;

    .explain-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .explain-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-left: 8px;
      }
    }

    .explain-content {
      font-size: 14px;
      color: #666;
      line-height: 1.6;
    }
  }
}

/* 底部支付栏 */
.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);

  /* 用户协议区域 */
  .agreement-section {
    padding: 15px 20px 10px;
    border-bottom: 1px solid #f0f0f0;

    .agreement-content {
      display: flex;
      align-items: center;
      justify-content: center;

      .agreement-checkbox {
        width: 18px;
        height: 18px;
        border: 2px solid #ddd;
        border-radius: 4px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &.agreement-checked {
          background: #00d4aa;
          border-color: #00d4aa;
        }
      }

      .agreement-text {
        font-size: 13px;
        color: #666;
        margin-right: 4px;
      }

      .agreement-link {
        font-size: 13px;
        color: #00d4aa;
        font-weight: 500;
      }
    }
  }

  /* 支付操作区域 */
  .payment-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;

    .payment-method {
      display: flex;
      align-items: center;

      .payment-icon {
        margin-right: 12px;

        .wx-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
        }
      }

      .payment-text {
        font-size: 16px;
        color: #333;
        font-weight: 500;
      }
    }

    .payment-action {
      .submit-btn {
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
        padding: 12px 30px;
        border-radius: 25px;
        box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.98);
        }

        text {
          color: white;
          font-size: 16px;
          font-weight: 500;
          margin-left: 6px;
        }
      }
    }
  }
}
</style>
