// 配置管理器 - 统一管理多公司配置
import ylstConfig from './companies/ylst.js';
import airuiConfig from './companies/airui.js';

// 当前激活的公司配置 (可通过脚本切换)
let currentCompany = 'ylst';

// 公司配置映射
const companyConfigs = {
  ylst: ylstConfig,
  airui: airuiConfig
};

/**
 * 配置管理器类
 */
class ConfigManager {
  constructor() {
    this.currentConfig = companyConfigs[currentCompany];
  }

  /**
   * 获取当前公司配置
   */
  getCurrentConfig() {
    return this.currentConfig;
  }

  /**
   * 获取基础信息配置
   */
  getCompanyInfo() {
    return this.currentConfig.companyInfo;
  }

  /**
   * 获取API配置
   */
  getApiConfig() {
    return this.currentConfig.api;
  }

  /**
   * 获取客服配置
   */
  getCustomerServiceConfig() {
    return this.currentConfig.customerService;
  }

  /**
   * 获取第三方小程序配置
   */
  getMiniProgramsConfig() {
    return this.currentConfig.miniPrograms;
  }

  /**
   * 获取法律文档配置
   */
  getLegalConfig() {
    return this.currentConfig.legal;
  }

  /**
   * 获取页面标题配置
   */
  getPageTitlesConfig() {
    return this.currentConfig.pageTitles;
  }

  /**
   * 切换公司配置 (运行时切换，主要用于调试)
   */
  switchCompany(companyKey) {
    if (companyConfigs[companyKey]) {
      currentCompany = companyKey;
      this.currentConfig = companyConfigs[companyKey];
      console.log(`已切换到: ${this.currentConfig.companyInfo.name}`);
      return true;
    }
    console.error(`未找到公司配置: ${companyKey}`);
    return false;
  }

  /**
   * 获取当前公司标识
   */
  getCurrentCompanyKey() {
    return currentCompany;
  }

  /**
   * 获取所有可用的公司配置
   */
  getAvailableCompanies() {
    return Object.keys(companyConfigs).map(key => ({
      key,
      name: companyConfigs[key].companyInfo.name,
      miniProgramName: companyConfigs[key].companyInfo.miniProgramName
    }));
  }
}

// 创建单例实例
const configManager = new ConfigManager();

export default configManager;
