<template>
  <view class="protocol-container">
    <scroll-view scroll-y="true" class="protocol-content">
      <view class="protocol-text">
        {{ userAgreementText }}
      </view>
    </scroll-view>
  </view>
</template>

<script>
import configManager from '@/configs/configManager.js';

export default {
  name: 'UserProtocol',
  data() {
    return {
      userAgreementText: ''
    };
  },
  onLoad() {
    this.loadUserAgreement();
  },
  methods: {
    loadUserAgreement() {
      const legalConfig = configManager.getLegalConfig();
      this.userAgreementText = legalConfig.userAgreement;
    }
  }
};
</script>

<style scoped>
.protocol-container {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.protocol-content {
  flex: 1;
  padding: 20rpx;
}

.protocol-text {
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
  white-space: pre-line;
  word-wrap: break-word;
}
</style>
