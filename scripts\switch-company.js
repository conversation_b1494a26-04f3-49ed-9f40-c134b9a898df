const fs = require('fs');
const path = require('path');

// 公司配置映射
const companyConfigs = {
  ylst: {
    name: "一路顺通",
    miniProgramName: "乐行车顺通",
    appId: "wxaefdb39f5390ee48",
    baseUrl: "https://www.ylst-etc.cn/api",
    customerService: {
      phoneNumber: "4000067882",
      corpId: "wwbab7752b3b3644d2",
      customerServiceUrl: "https://work.weixin.qq.com/kfid/kfca0fd2328faf798ce"
    }
  },
  airui: {
    name: "爱睿",
    miniProgramName: "E卡畅通ETC",
    appId: "wx486870806bbf30df",
    baseUrl: "https://drtjza50.beesnat.com",
    customerService: {
      phoneNumber: "4000067882",
      corpId: "wwbab7752b3b3644d2",
      customerServiceUrl: "https://work.weixin.qq.com/kfid/kfca0fd2328faf798ce"
    }
  }
};

/**
 * 更新 manifest.json 文件
 * @param {string} companyKey - 公司标识
 */
function updateManifest(companyKey) {
  const config = companyConfigs[companyKey];
  const manifestPath = path.join(__dirname, '../manifest.json');
  
  try {
    const manifestContent = fs.readFileSync(manifestPath, 'utf8');
    const manifest = JSON.parse(manifestContent);
    
    // 更新小程序名称和AppID
    manifest.name = config.miniProgramName;
    manifest['mp-weixin'].appid = config.appId;
    
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 4));
    console.log(`✅ 已更新 manifest.json - 小程序名称: ${config.miniProgramName}, AppID: ${config.appId}`);
  } catch (error) {
    console.error('❌ 更新 manifest.json 失败:', error.message);
  }
}

/**
 * 更新 project.config.json 文件
 * @param {string} companyKey - 公司标识
 */
function updateProjectConfig(companyKey) {
  const config = companyConfigs[companyKey];
  const projectConfigPath = path.join(__dirname, '../project.config.json');
  
  try {
    const projectConfigContent = fs.readFileSync(projectConfigPath, 'utf8');
    const projectConfig = JSON.parse(projectConfigContent);
    
    // 更新AppID
    projectConfig.appid = config.appId;
    
    fs.writeFileSync(projectConfigPath, JSON.stringify(projectConfig, null, 2));
    console.log(`✅ 已更新 project.config.json - AppID: ${config.appId}`);
  } catch (error) {
    console.error('❌ 更新 project.config.json 失败:', error.message);
  }
}

/**
 * 更新配置管理器的默认公司
 * @param {string} companyKey - 公司标识
 */
function updateConfigManager(companyKey) {
  const configManagerPath = path.join(__dirname, '../configs/configManager.js');

  try {
    let content = fs.readFileSync(configManagerPath, 'utf8');

    // 替换默认公司配置
    content = content.replace(
      /let currentCompany = '[^']*';/,
      `let currentCompany = '${companyKey}';`
    );

    fs.writeFileSync(configManagerPath, content);
    console.log(`✅ 已更新配置管理器默认公司: ${companyConfigs[companyKey].name}`);
  } catch (error) {
    console.error('❌ 更新配置管理器失败:', error.message);
  }
}



/**
 * 主切换函数
 * @param {string} companyKey - 公司标识
 */
function switchCompany(companyKey) {
  if (!companyConfigs[companyKey]) {
    console.error(`❌ 未知的公司标识: ${companyKey}`);
    console.log('可用的公司标识:');
    Object.keys(companyConfigs).forEach(key => {
      console.log(`  - ${key}: ${companyConfigs[key].name} (${companyConfigs[key].miniProgramName})`);
    });
    return;
  }

  console.log(`🔄 正在切换到: ${companyConfigs[companyKey].name} (${companyConfigs[companyKey].miniProgramName})`);
  console.log('='.repeat(60));

  // 执行切换操作
  updateManifest(companyKey);
  updateProjectConfig(companyKey);
  updateConfigManager(companyKey);

  console.log('='.repeat(60));
  console.log(`✅ 切换完成！当前配置:`);
  console.log(`   公司: ${companyConfigs[companyKey].name}`);
  console.log(`   小程序名称: ${companyConfigs[companyKey].miniProgramName}`);
  console.log(`   AppID: ${companyConfigs[companyKey].appId}`);
  console.log(`   API地址: ${companyConfigs[companyKey].baseUrl}`);
  console.log(`   客服电话: ${companyConfigs[companyKey].customerService.phoneNumber}`);
  console.log(`   企业微信ID: ${companyConfigs[companyKey].customerService.corpId}`);
  console.log('');
  console.log('⚠️  请重新编译项目以使配置生效！');
  console.log('');
  console.log('📝 配置文件更新说明:');
  console.log('   ✓ manifest.json - 小程序名称和AppID');
  console.log('   ✓ project.config.json - 开发工具AppID');
  console.log('   ✓ configManager.js - 当前激活的公司配置');
  console.log('   ✓ 所有配置将通过配置管理器自动应用到项目中');
}

/**
 * 显示可用的公司列表
 */
function showCompanyList() {
  console.log('📋 可用的公司配置:');
  console.log('='.repeat(60));
  Object.keys(companyConfigs).forEach(key => {
    const config = companyConfigs[key];
    console.log(`🏢 ${key.toUpperCase()}`);
    console.log(`   公司名称: ${config.name}`);
    console.log(`   小程序名称: ${config.miniProgramName}`);
    console.log(`   AppID: ${config.appId}`);
    console.log(`   API地址: ${config.baseUrl}`);
    console.log(`   客服电话: ${config.customerService.phoneNumber}`);
    console.log('');
  });
  console.log('💡 使用方法:');
  console.log('   npm run switch:ylst   - 切换到一路顺通');
  console.log('   npm run switch:airui  - 切换到爱睿');
  console.log('   npm run switch:list   - 显示此列表');
}

// 命令行参数处理
const args = process.argv.slice(2);
if (args.length === 0) {
  console.log('❌ 请指定公司标识！');
  console.log('');
  showCompanyList();
} else if (args[0] === '--list' || args[0] === '-l') {
  showCompanyList();
} else {
  switchCompany(args[0]);
}

module.exports = { switchCompany, companyConfigs };
